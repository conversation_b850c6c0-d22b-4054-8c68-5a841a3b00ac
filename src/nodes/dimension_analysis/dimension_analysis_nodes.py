from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langgraph.types import Command

from src.nodes.dimension_analysis.base_dimension_node import BaseDimensionNode
from src.nodes.dimension_analysis.prompts import (
    dimension_trend_prompt_template,
    error_distribution_prompt_template,
    metric_trend_prompt_template,
)
from src.state import State
from src.zego_tools import DataQueryParams


async def worker_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """
    通用分析节点，接受query_param参数进行分析
    从state中获取current_query_params作为分析参数
    """
    query_params = state.get("current_query_params")
    if not query_params:
        raise ValueError("未找到current_query_params，无法执行数据查询分析")

    # 根据query_params选择合适的模板
    if query_params.is_error_distribution_metric():
        prompt_template = error_distribution_prompt_template
    elif query_params.digdimension:
        prompt_template = dimension_trend_prompt_template
    else:
        prompt_template = metric_trend_prompt_template

    return await BaseDimensionNode(
        prompt_template=prompt_template,
        query_params=query_params
    ).execute(state, config, store=store)


# 保留原有节点函数以保持向后兼容性
async def metric_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """指标趋势分析节点"""
    return await BaseDimensionNode(
        prompt_template=metric_trend_prompt_template,
        analysis_key="metric_trend_analysis",
        analysis_name="指标趋势分析",
    ).execute(state, config, store=store)


async def error_distribution_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """错误码分布分析节点"""
    return await BaseDimensionNode(
        prompt_template=error_distribution_prompt_template,
        analysis_key="error_distribution_analysis",
        analysis_name="错误码分布分析",
    ).execute(state, config, store=store)


async def dimension_trend_analysis_node(state: State, config: RunnableConfig, *, store: BaseStore) -> Command:
    """通用维度下钻分析节点 - 处理所有维度的下钻分析"""
    # 直接使用通用的维度分析模板，支持动态维度
    return await BaseDimensionNode(
        prompt_template=dimension_trend_prompt_template,
    ).execute(state, config, store=store)



