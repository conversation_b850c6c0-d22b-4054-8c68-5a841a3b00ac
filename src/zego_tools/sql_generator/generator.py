"""重构后的SQL生成器核心模块

专门负责根据查询参数生成对应的SQL查询语句。
"""

from datetime import datetime

from .metrics import metrics_registry, TableGranularityManager, ZEGOMetric
from .params import DataQueryParams

# 删除旧的templates依赖，现在所有SQL构建逻辑都在本文件中


def generate_sql(data_query_params: DataQueryParams) -> str:
    """
    使用查询参数生成对应的SQL语句

    Args:
        data_query_params: 数据查询参数对象

    Returns:
        生成的SQL查询语句
    """
    start_time = datetime.strptime(data_query_params.time_start, "%Y-%m-%d %H:%M:%S")
    end_time = datetime.strptime(data_query_params.time_end, "%Y-%m-%d %H:%M:%S")

    # 根据时间范围确定数据粒度
    granularity = TableGranularityManager.auto_select_granularity(start_time, end_time)

    # 构建过滤条件
    filters = {}
    if data_query_params.appid_filter:
        filters["app_id"] = data_query_params.appid_filter
    if data_query_params.country_filter:
        filters["country"] = data_query_params.country_filter

    # 判断是否为错误码分布指标
    is_error_distribution = data_query_params.is_error_distribution_metric()

    # 获取基础指标名称（用于查找指标配置）
    base_metric_name = data_query_params.get_base_metric_name()

    # 获取指标配置
    metric = metrics_registry.get(base_metric_name)
    if not metric:
        raise ValueError(f"未找到指标配置: {base_metric_name}")

    # 如果是错误码分布指标，验证该指标是否支持错误码分布分析
    if is_error_distribution and not metric.supports_error_distribution:
        raise ValueError(f"指标 {base_metric_name} 不支持错误码分布分析")

    # 验证下钻维度是否被指标支持
    if data_query_params.digdimension:
        if not metric.supports_dimension(data_query_params.digdimension):
            available_dims = metric.get_available_dimensions()
            raise ValueError(
                f"指标 {base_metric_name} 不支持维度 {data_query_params.digdimension}。"
                f"可用维度: {', '.join(available_dims)}"
            )

    if data_query_params.digdimension:
        # 维度下钻分析
        if is_error_distribution:
            # 错误码分布的维度下钻分析
            return build_error_distribution_with_dimension_query(
                metric=metric,
                dimension=data_query_params.digdimension,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
                extra_where=data_query_params.where,
            )
        else:
            # 默认指标的维度下钻分析
            return build_dimension_trend_query(
                metric=metric,
                dimension=data_query_params.digdimension,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
                extra_where=data_query_params.where,
            )
    else:
        # 普通趋势分析（无下钻维度）
        if is_error_distribution:
            # 错误码分布分析
            return build_error_distribution_query(
                metric=metric,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
                extra_where=data_query_params.where,
            )
        else:
            # 默认指标趋势分析
            return build_trend_query(
                metric=metric,
                start_time=start_time,
                end_time=end_time,
                filters=filters,
                granularity=granularity,
                extra_where=data_query_params.where,
            )


# 统一的SQL构建辅助函数
def _build_where_conditions(metric, start_time, end_time, filters, extra_where=None):
    """构建WHERE条件"""
    where_conditions = [
        f"{metric.time_field} BETWEEN '{start_time.strftime('%Y-%m-%d %H:%M:%S')}' AND '{end_time.strftime('%Y-%m-%d %H:%M:%S')}'"
    ]

    # 添加指标固有的过滤条件
    if metric.where_filters:
        where_conditions.append(metric.where_filters)

    # 添加用户过滤条件
    for field, value in filters.items():
        where_conditions.append(f"{field} = '{value}'")

    # 添加额外的WHERE条件
    if extra_where:
        where_conditions.append(f"({extra_where})")

    return where_conditions


def build_trend_query(metric, start_time, end_time, filters, granularity, extra_where=None) -> str:
    """统一的趋势查询构建函数"""
    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters, extra_where)

    sql = f"""
SELECT
    {metric.time_field},
    {metric.value_sql} as metric_value,
    SUM({metric.sample_ct_field}) as total_count
FROM {table_name}
WHERE {' AND '.join(where_conditions)}
GROUP BY {metric.time_field}
ORDER BY {metric.time_field}
    """
    return sql


def build_error_distribution_query(metric, start_time, end_time, filters, granularity, extra_where=None) -> str:
    """构建错误码分布查询"""
    if not metric.supports_error_distribution:
        raise ValueError(f"指标 {metric.name} 不支持错误码分布分析")

    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters, extra_where)

    return metric.get_error_distribution_sql(table_name, where_conditions, granularity)


def build_error_distribution_with_dimension_query(metric, dimension, start_time, end_time, filters, granularity, extra_where=None) -> str:
    """构建带维度下钻的错误码分布查询"""
    if not metric.supports_error_distribution:
        raise ValueError(f"指标 {metric.name} 不支持错误码分布分析")

    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters, extra_where)

    return metric.get_error_distribution_with_dimension_sql(table_name, where_conditions, dimension, granularity)


def build_dimension_trend_query(
    metric, dimension, start_time, end_time, filters, granularity, extra_where=None
) -> str:
    """构建维度趋势分析查询"""
    table_name = metric.get_table_name(granularity)
    where_conditions = _build_where_conditions(metric, start_time, end_time, filters, extra_where)

    # 使用CTE优化查询并限制维度数量
    sql = f"""
WITH daily_stats AS (
    SELECT
        {metric.time_field},
        {dimension},
        {metric.value_sql} as metric_value,
        SUM({metric.sample_ct_field}) as total_count
    FROM {table_name}
    WHERE {' AND '.join(where_conditions)}
    GROUP BY {metric.time_field}, {dimension}
),
top_dimensions AS (
    SELECT
        {dimension},
        SUM(total_count) as dimension_total
    FROM daily_stats
    GROUP BY {dimension}
    ORDER BY dimension_total DESC
    LIMIT 10
)
SELECT
    ds.{metric.time_field},
    ds.{dimension},
    ds.metric_value,
    ds.total_count
FROM daily_stats ds
INNER JOIN top_dimensions td ON ds.{dimension} = td.{dimension}
ORDER BY ds.{metric.time_field}, td.dimension_total DESC
    """
    return sql
