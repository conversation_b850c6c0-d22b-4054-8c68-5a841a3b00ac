"""数据查询参数定义模块"""

from datetime import datetime, timedelta
from typing import Literal, Optional

from langchain_core.load.serializable import Serializable
from pydantic import Field


class DataQueryParams(Serializable):
    """数据查询参数类，定义了所有查询相关的参数和配置"""

    metric_name: Literal[
        # 默认指标
        "拉流成功率",
        "登录成功率",
        "5s登录成功率",
        "推流成功率",
        "视频卡顿率",
        "拉流丢包率",
        "推流丢包率",
        "统一接入request成功率",
        "统一接入connect成功率",
        "3s拉流请求成功率",
        "3s推流请求成功率",
        "推流rtc子事件成功率",
        "拉流rtc子事件成功率",
        "拉流l3子事件成功率",
        "拉流调度成功率",
        "推流调度成功率",
        "视频首帧",
        "拉流请求耗时均值",
        "推流请求耗时均值",
        "拉流请求耗时1s占比",
        "推流请求耗时1s占比",
        "端到端丢包率",
        # 错误码分布指标
        "拉流错误码分布",
        "登录错误码分布",
        "5s登录错误码分布",
        "推流错误码分布",
        "统一接入request错误码分布",
        "统一接入connect错误码分布",
        "3s拉流请求错误码分布",
        "3s推流请求错误码分布",
        "推流rtc子事件错误码分布",
        "拉流rtc子事件错误码分布",
        "拉流l3子事件错误码分布",
        "拉流调度错误码分布",
        "推流调度错误码分布",
    ] = Field(default="拉流成功率", description="指标名称，包括默认指标和错误码分布指标")

    digdimension: Optional[str] = Field(
        default=None,
        description="下钻维度字段名，如 'country'、'sdk_version'、'isp'、'platform' 等。如果为空则进行普通趋势分析，如果指定则进行该维度的下钻分析",
    )

    where: Optional[str] = Field(
        default=None,
        description="额外的 WHERE 条件，如 \"platform='Android'\" 或 \"country='中国' AND platform='iOS'\"。用于进一步过滤数据",
    )

    time_start: str = Field(
        default=(datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d %H:%M:%S"),
        description="数据查询的开始时间，默认30天前；注意，不同时间范围数据精度不同，7天以上数据为天粒度，7天内为小时粒度，2天内为10分钟粒度，4小时内为分钟粒度；格式为YYYY-MM-DD HH:MM:SS",
    )
    time_end: str = Field(
        default=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        description="数据查询的结束时间，默认当前时间；注意，不同时间范围数据精度不同，7天以上数据为天粒度，7天内为小时粒度，2天内为10分钟粒度，4小时内为分钟粒度；格式为YYYY-MM-DD HH:MM:SS。",
    )

    appid_filter: Optional[int] = Field(
        default=None,
        description="数据查询的客户AppID过滤条件, 如123456",
    )

    country_filter: Optional[str] = Field(
        default=None,
        description="数据查询的国家过滤条件, 如'沙特阿拉伯'，注意，country使用中文而非国家代码",
    )

    def to_prompt(self) -> str:
        """
        将查询参数转换为大模型可理解的提示词格式
        帮助大模型理解当前数据的查询条件和背景
        """
        time_range = f"{self.time_start} 至 {self.time_end}"

        prompt_parts = [
            "**数据查询参数说明**：",
            f"• 指标：{self.metric_name}",
            f"• 时间范围：{time_range}",
        ]

        if self.digdimension:
            prompt_parts.append(f"• 下钻维度：{self.digdimension}")

        if self.where:
            prompt_parts.append(f"• 额外过滤条件：{self.where}")

        if self.appid_filter:
            prompt_parts.append(f"• 客户AppID过滤：{self.appid_filter}")

        if self.country_filter:
            prompt_parts.append(f"• 国家过滤：{self.country_filter}")

        # 添加查询参数的含义说明
        analysis_desc = "维度下钻分析" if self.digdimension else "指标趋势分析"

        prompt_parts.extend(
            [
                "",
                "**参数含义说明**：",
                f"- 本次查询针对 **{self.metric_name}** 指标进行 **{analysis_desc}**",
                f"- 数据时间范围为 **{time_range}**",
            ]
        )

        if self.digdimension:
            prompt_parts.append(f"- 按 **{self.digdimension}** 维度进行下钻分析")

        if self.where:
            prompt_parts.append(f"- 应用额外过滤条件：**{self.where}**")

        if self.appid_filter:
            prompt_parts.append(f"- 数据已过滤，仅包含客户AppID为 **{self.appid_filter}** 的数据")

        if self.country_filter:
            prompt_parts.append(f"- 数据已过滤，仅包含 **{self.country_filter}** 地区的数据")
        else:
            prompt_parts.append("- 数据包含全球所有地区")

        if not self.appid_filter:
            prompt_parts.append("- 数据包含所有客户AppID")

        # 为成功率指标添加错误码分布分析提示
        if self.metric_name.endswith("成功率") and not self.metric_name.endswith("错误码分布"):
            prompt_parts.append("- **重要提示**：分析成功率问题时，建议同时关注对应的错误码分布指标")

        prompt_parts.extend(["", "**重要提醒**：分析时请基于以上查询条件来理解数据的覆盖范围和局限性。"])

        return "\n".join(prompt_parts)

    def is_error_distribution_metric(self) -> bool:
        """
        判断当前指标是否为错误码分布指标

        Returns:
            bool: 如果是错误码分布指标返回True，否则返回False
        """
        return self.metric_name.endswith("错误码分布")

    def get_base_metric_name(self) -> str:
        """
        获取基础指标名称（去除"错误码分布"后缀）

        Returns:
            str: 基础指标名称
        """
        if self.is_error_distribution_metric():
            return self.metric_name.replace("错误码分布", "成功率")
        return self.metric_name

    @classmethod
    def is_lc_serializable(cls) -> bool:
        """Return whether this model can be serialized by Langchain."""
        return True

    @classmethod
    def get_lc_namespace(cls) -> list[str]:
        """Get the namespace of the langchain object."""
        return ["src", "zego_tools", "sql_generator"]
