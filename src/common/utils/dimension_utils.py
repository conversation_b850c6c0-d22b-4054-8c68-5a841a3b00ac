"""
维度分析工具函数：统一处理维度映射和分析信息生成
用于替代原有的复杂映射逻辑和固定的dimension_key
"""
from typing import Tuple
from src.zego_tools import DataQueryParams
from src.zego_tools.sql_generator.metrics import get_dimension_display_name


def get_analysis_info_from_query_params(query_param: DataQueryParams) -> <PERSON><PERSON>[str, str]:
    """
    从查询参数直接获取分析键和分析名称

    Args:
        query_param: 数据查询参数

    Returns:
        Tuple[str, str]: (analysis_key, analysis_name)
        - analysis_key: 用于节点路由和数据存储的键
        - analysis_name: 用于日志和LLM提示的显示名称
    """
    if query_param.is_error_distribution_metric():
        analysis_key = "error_distribution_analysis"
        analysis_name = f"{query_param.metric_name}分析"
    elif query_param.digdimension:
        # 维度下钻分析 - 支持任意维度，不再限定于固定的几个
        # 获取显示名称，如果没有映射则使用原始字段名
        display_name = get_dimension_display_name(query_param.digdimension)

        analysis_key = f"{query_param.digdimension}_trend_analysis"
        analysis_name = f"{display_name}维度{query_param.metric_name}分析"
    else:
        # 普通趋势分析
        analysis_key = "metric_trend_analysis"
        analysis_name = f"{query_param.metric_name}趋势分析"

    return analysis_key, analysis_name


def get_node_name_from_analysis_key(analysis_key: str) -> str:
    """
    从分析键获取对应的节点名称
    
    Args:
        analysis_key: 分析键
        
    Returns:
        str: 节点名称
    """
    # 维度下钻分析统一映射到通用的维度分析节点
    if analysis_key.endswith("_trend_analysis") and analysis_key != "metric_trend_analysis":
        return "dimension_trend_analysis"
    
    # 其他分析类型的直接映射
    node_mapping = {
        "metric_trend_analysis": "metric_trend_analysis",
        "error_distribution_analysis": "error_distribution_analysis",
    }
    
    return node_mapping.get(analysis_key, analysis_key)


def get_query_title_from_params(query_param: DataQueryParams) -> str:
    """
    从查询参数生成查询标题

    Args:
        query_param: 数据查询参数

    Returns:
        str: 查询标题
    """
    if query_param.is_error_distribution_metric():
        return "错误码分布分析数据"
    elif query_param.digdimension:
        display_name = get_dimension_display_name(query_param.digdimension)
        return f"{display_name}维度下钻分析数据"
    else:
        return "指标趋势分析数据"